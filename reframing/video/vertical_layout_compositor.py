#!/usr/bin/env python3
"""
Vertical Layout Compositor for Two-Face Podcast Videos

This module implements a vertical video layout system that creates a single 720x1280 video
with two faces stacked vertically. The left-side face is positioned at the top half,
and the right-side face is positioned at the bottom half, both centered horizontally.

Features:
- Single vertical video output (720x1280)
- Left face in top half (720x640)
- Right face in bottom half (720x640)
- Real-time face tracking and cropping
- Predictive face positioning integration
- Smooth transitions between face positions
- Efficient single-pass FFmpeg processing
"""

import logging
import os
import subprocess
import tempfile
import json
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

try:
    from ..models.data_classes import FaceDetection
    from .face_positioning import FacePositioningEngine, FaceLayoutType, FacePositionLayout
except ImportError:
    # Fallback for testing
    from dataclasses import dataclass as _dataclass

    @_dataclass
    class FaceDetection:
        x: int
        y: int
        width: int
        height: int
        confidence: float
        center_x: float
        center_y: float


@dataclass
class VerticalLayoutOutput:
    """Container for vertical layout output information"""
    video_path: str
    metadata: Dict[str, Any]
    face_tracks: Dict[str, Any]
    layout_data: Dict[str, Any]


@dataclass
class FaceRegion:
    """Face region definition for vertical layout"""
    face_id: str  # "left" or "right"
    region_name: str  # "top_half" or "bottom_half"
    x: int
    y: int
    width: int
    height: int
    crop_x: int
    crop_y: int
    crop_width: int
    crop_height: int


class VerticalLayoutCompositor:
    """
    Advanced vertical layout compositor for two-face podcast scenarios
    
    Creates a single vertical video (720x1280) with two faces stacked vertically:
    - Top half (720x640): Left-side face, centered horizontally
    - Bottom half (720x640): Right-side face, centered horizontally
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Video specifications for vertical layout
        self.target_width = 720
        self.target_height = 1280
        self.region_height = 640  # Half of target height
        
        # Face positioning engines for predictive tracking
        self.left_face_engine = FacePositioningEngine()
        self.right_face_engine = FacePositioningEngine()
        
        # Configuration
        self.fps = 30
        self.video_codec = 'libx264'
        self.audio_codec = 'aac'
        self.quality_preset = 'fast'
        self.crf = 23
        
        # Layout configuration
        self.face_padding = 50  # Padding around faces
        self.transition_smoothing = 0.3  # Smoothing factor for position transitions
        self.min_face_size = 100  # Minimum face size for processing

    def process_vertical_layout_video(self, input_video_path: str, face_data_sequence: List[Dict[str, Any]],
                                    output_dir: str, base_filename: str) -> VerticalLayoutOutput:
        """
        Process video with dual face detection to create vertical layout output
        
        Args:
            input_video_path: Path to source video
            face_data_sequence: Sequence of face detection data with timestamps
            output_dir: Output directory for generated video
            base_filename: Base filename for output video
            
        Returns:
            VerticalLayoutOutput containing path and metadata
        """
        self.logger.info("🎬 Starting vertical layout compositing for two-face scenario")
        
        # Validate input
        if not self._validate_dual_face_data(face_data_sequence):
            raise ValueError("Face data sequence does not contain consistent dual face detection")
        
        # Separate and analyze face tracks
        left_track, right_track = self._separate_face_tracks(face_data_sequence)
        
        # Generate face positioning layouts for each track
        self._generate_face_layouts(left_track, right_track)
        
        # Create output path
        output_path = os.path.join(output_dir, f"{base_filename}_vertical_layout.mp4")
        
        # Generate vertical layout video
        self.logger.info("📹 Generating vertical layout video")
        success = self._generate_vertical_layout_video(
            input_video_path, output_path, left_track, right_track
        )
        
        if not success:
            raise RuntimeError("Failed to generate vertical layout video")
        
        # Create metadata
        metadata = self._create_layout_metadata(left_track, right_track, face_data_sequence)
        
        # Create face tracks data
        face_tracks = self._create_face_tracks_data(left_track, right_track)
        
        # Create layout data
        layout_data = self._create_layout_data(left_track, right_track)
        
        self.logger.info("✅ Vertical layout compositing completed successfully")
        
        return VerticalLayoutOutput(
            video_path=output_path,
            metadata=metadata,
            face_tracks=face_tracks,
            layout_data=layout_data
        )

    def _validate_dual_face_data(self, face_data_sequence: List[Dict[str, Any]]) -> bool:
        """Validate that face data contains consistent dual face detection"""
        if not face_data_sequence:
            return False
        
        # Check that most frames have exactly 2 faces
        dual_face_frames = 0
        for frame_data in face_data_sequence:
            if len(frame_data.get('faces', [])) == 2:
                dual_face_frames += 1
        
        # Require at least 70% of frames to have dual faces
        dual_face_ratio = dual_face_frames / len(face_data_sequence)
        return dual_face_ratio >= 0.7

    def _separate_face_tracks(self, face_data_sequence: List[Dict[str, Any]]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Separate face detections into left and right tracks"""
        left_detections = []
        right_detections = []
        left_timestamps = []
        right_timestamps = []
        
        for frame_data in face_data_sequence:
            faces = frame_data.get('faces', [])
            timestamp = frame_data.get('timestamp', 0.0)
            
            if len(faces) == 2:
                # Sort faces by x-coordinate (leftmost first)
                sorted_faces = sorted(faces, key=lambda f: f.get('center_x', f.get('x', 0)))
                
                # Convert to FaceDetection objects
                left_face = self._dict_to_face_detection(sorted_faces[0])
                right_face = self._dict_to_face_detection(sorted_faces[1])
                
                left_detections.append(left_face)
                right_detections.append(right_face)
                left_timestamps.append(timestamp)
                right_timestamps.append(timestamp)
        
        left_track = {
            'face_id': 'left',
            'region': 'top_half',
            'face_detections': left_detections,
            'timestamps': left_timestamps,
            'layouts': []
        }
        
        right_track = {
            'face_id': 'right',
            'region': 'bottom_half',
            'face_detections': right_detections,
            'timestamps': right_timestamps,
            'layouts': []
        }
        
        return left_track, right_track

    def _dict_to_face_detection(self, face_dict: Dict[str, Any]) -> FaceDetection:
        """Convert face dictionary to FaceDetection object"""
        return FaceDetection(
            x=face_dict.get('x', 0),
            y=face_dict.get('y', 0),
            width=face_dict.get('width', 100),
            height=face_dict.get('height', 100),
            confidence=face_dict.get('confidence', 0.8),
            center_x=face_dict.get('center_x', face_dict.get('x', 0) + face_dict.get('width', 100) / 2),
            center_y=face_dict.get('center_y', face_dict.get('y', 0) + face_dict.get('height', 100) / 2)
        )

    def _generate_face_layouts(self, left_track: Dict[str, Any], right_track: Dict[str, Any]):
        """Generate face positioning layouts for each track"""
        self.logger.info("🎯 Generating face layouts for vertical positioning")

        # Process left face track (top region)
        for i, (face, timestamp) in enumerate(zip(left_track['face_detections'], left_track['timestamps'])):
            layout = self.left_face_engine.calculate_face_positioning(
                faces=[face],
                frame_width=1920,  # Assume standard input resolution
                frame_height=1080,
                target_width=self.target_width,
                target_height=self.region_height,  # Use region height for individual face
                timestamp=timestamp
            )
            left_track['layouts'].append(layout)

        # Process right face track (bottom region)
        for i, (face, timestamp) in enumerate(zip(right_track['face_detections'], right_track['timestamps'])):
            layout = self.right_face_engine.calculate_face_positioning(
                faces=[face],
                frame_width=1920,  # Assume standard input resolution
                frame_height=1080,
                target_width=self.target_width,
                target_height=self.region_height,  # Use region height for individual face
                timestamp=timestamp
            )
            right_track['layouts'].append(layout)

        self.logger.info(f"✅ Generated {len(left_track['layouts'])} layouts for left track")
        self.logger.info(f"✅ Generated {len(right_track['layouts'])} layouts for right track")

    def _generate_vertical_layout_video(self, input_path: str, output_path: str,
                                      left_track: Dict[str, Any], right_track: Dict[str, Any]) -> bool:
        """
        Generate vertical layout video using FFmpeg complex filter

        Args:
            input_path: Source video path
            output_path: Output video path
            left_track: Left face track data
            right_track: Right face track data

        Returns:
            True if successful
        """
        try:
            # Create crop filters for each face track
            left_crop_filter = self._create_track_crop_filter(left_track, "top")
            right_crop_filter = self._create_track_crop_filter(right_track, "bottom")

            # Build complex FFmpeg filter for vertical stacking
            filter_complex = (
                f"[0:v]{left_crop_filter},scale={self.target_width}:{self.region_height}[top];"
                f"[0:v]{right_crop_filter},scale={self.target_width}:{self.region_height}[bottom];"
                f"[top][bottom]vstack=inputs=2[v]"
            )

            # Build FFmpeg command
            cmd = [
                'ffmpeg', '-y',
                '-i', input_path,
                '-filter_complex', filter_complex,
                '-map', '[v]',
                '-map', '0:a',  # Copy audio from source
                '-c:v', self.video_codec,
                '-c:a', self.audio_codec,
                '-preset', self.quality_preset,
                '-crf', str(self.crf),
                '-r', str(self.fps),
                '-movflags', '+faststart',
                output_path
            ]

            self.logger.info(f"🎬 Generating vertical layout video: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                self.logger.info(f"✅ Successfully generated vertical layout video: {output_path}")
                return True
            else:
                self.logger.error(f"❌ Failed to generate vertical layout video: {result.stderr}")
                return False

        except Exception as e:
            self.logger.error(f"❌ Error generating vertical layout video: {str(e)}")
            return False

    def _create_track_crop_filter(self, track: Dict[str, Any], position: str) -> str:
        """Create FFmpeg crop filter for a face track"""
        if not track['layouts']:
            # Fallback to center crop based on position
            if position == "top":
                return f"crop={self.target_width}:{self.region_height}:600:100"
            else:  # bottom
                return f"crop={self.target_width}:{self.region_height}:600:300"

        # Use the first layout for now (can be enhanced with temporal interpolation)
        layout = track['layouts'][0]

        # Calculate crop position ensuring it fits within the source frame
        crop_x = max(0, min(1920 - self.target_width, layout.crop_x))
        crop_y = max(0, min(1080 - self.region_height, layout.crop_y))

        return f"crop={self.target_width}:{self.region_height}:{crop_x}:{crop_y}"

    def _create_layout_metadata(self, left_track: Dict[str, Any], right_track: Dict[str, Any],
                               face_data_sequence: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create metadata for vertical layout output"""
        return {
            'generation_info': {
                'compositor_version': '1.0',
                'layout_type': 'vertical_two_face',
                'face_positioning_engine': 'enhanced_predictive',
                'total_frames': len(face_data_sequence),
                'dual_face_frames': len(left_track['face_detections'])
            },
            'layout_specifications': {
                'total_dimensions': f"{self.target_width}x{self.target_height}",
                'top_region': {
                    'face_id': 'left',
                    'dimensions': f"{self.target_width}x{self.region_height}",
                    'position': 'top_half',
                    'y_offset': 0
                },
                'bottom_region': {
                    'face_id': 'right',
                    'dimensions': f"{self.target_width}x{self.region_height}",
                    'position': 'bottom_half',
                    'y_offset': self.region_height
                }
            },
            'face_analysis': {
                'left_face': {
                    'face_count': len(left_track['face_detections']),
                    'average_confidence': self._calculate_average_confidence(left_track),
                    'region_assignment': 'top_half'
                },
                'right_face': {
                    'face_count': len(right_track['face_detections']),
                    'average_confidence': self._calculate_average_confidence(right_track),
                    'region_assignment': 'bottom_half'
                }
            },
            'video_settings': {
                'codec': self.video_codec,
                'audio_codec': self.audio_codec,
                'fps': self.fps,
                'quality_preset': self.quality_preset,
                'crf': self.crf
            }
        }

    def _create_face_tracks_data(self, left_track: Dict[str, Any], right_track: Dict[str, Any]) -> Dict[str, Any]:
        """Create face tracks data for analysis"""
        return {
            'left_track': {
                'face_id': left_track['face_id'],
                'region': left_track['region'],
                'detection_count': len(left_track['face_detections']),
                'timestamp_range': {
                    'start': min(left_track['timestamps']) if left_track['timestamps'] else 0.0,
                    'end': max(left_track['timestamps']) if left_track['timestamps'] else 0.0
                },
                'layout_count': len(left_track['layouts'])
            },
            'right_track': {
                'face_id': right_track['face_id'],
                'region': right_track['region'],
                'detection_count': len(right_track['face_detections']),
                'timestamp_range': {
                    'start': min(right_track['timestamps']) if right_track['timestamps'] else 0.0,
                    'end': max(right_track['timestamps']) if right_track['timestamps'] else 0.0
                },
                'layout_count': len(right_track['layouts'])
            }
        }

    def _create_layout_data(self, left_track: Dict[str, Any], right_track: Dict[str, Any]) -> Dict[str, Any]:
        """Create layout data for the vertical composition"""
        return {
            'layout_type': 'vertical_two_face_stack',
            'regions': {
                'top_region': {
                    'assigned_face': 'left',
                    'x': 0,
                    'y': 0,
                    'width': self.target_width,
                    'height': self.region_height
                },
                'bottom_region': {
                    'assigned_face': 'right',
                    'x': 0,
                    'y': self.region_height,
                    'width': self.target_width,
                    'height': self.region_height
                }
            },
            'face_positioning': {
                'left_face_layouts': len(left_track['layouts']),
                'right_face_layouts': len(right_track['layouts']),
                'predictive_tracking_enabled': True,
                'smooth_transitions': True
            }
        }

    def _calculate_average_confidence(self, track: Dict[str, Any]) -> float:
        """Calculate average confidence for a face track"""
        if not track['face_detections']:
            return 0.0

        total_confidence = sum(face.confidence for face in track['face_detections'])
        return total_confidence / len(track['face_detections'])

    @staticmethod
    def is_vertical_layout_scenario(face_data_sequence: List[Dict[str, Any]]) -> bool:
        """
        Check if the face data sequence is suitable for vertical layout

        Args:
            face_data_sequence: Sequence of face detection data

        Returns:
            True if this is suitable for vertical layout
        """
        if not face_data_sequence:
            return False

        # Count frames with exactly 2 faces
        dual_face_frames = sum(
            1 for frame_data in face_data_sequence
            if len(frame_data.get('faces', [])) == 2
        )

        # Require at least 70% of frames to have dual faces
        dual_face_ratio = dual_face_frames / len(face_data_sequence)
        return dual_face_ratio >= 0.7

    def get_debug_info(self) -> Dict[str, Any]:
        """Get debug information about the vertical layout compositor"""
        return {
            'compositor_config': {
                'target_width': self.target_width,
                'target_height': self.target_height,
                'region_height': self.region_height,
                'fps': self.fps,
                'video_codec': self.video_codec,
                'audio_codec': self.audio_codec,
                'quality_preset': self.quality_preset,
                'crf': self.crf
            },
            'layout_config': {
                'face_padding': self.face_padding,
                'transition_smoothing': self.transition_smoothing,
                'min_face_size': self.min_face_size
            },
            'face_engines': {
                'left_engine_history': len(self.left_face_engine.face_history),
                'right_engine_history': len(self.right_face_engine.face_history),
                'left_engine_segments': len(self.left_face_engine.segment_plans),
                'right_engine_segments': len(self.right_face_engine.segment_plans)
            }
        }
