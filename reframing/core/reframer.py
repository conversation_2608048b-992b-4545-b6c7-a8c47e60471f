#!/usr/bin/env python3
"""
Core Reframer class for intelligent video reframing

This is the main class that orchestrates face detection, speaker tracking,
and crop window calculation for optimal video reframing.
"""

import os
import json
import cv2
import ffmpeg
import tempfile
import logging
from typing import Dict, Any, Optional, List

from ..face_detection.engine import FaceDetectionEngine
from ..tracking.speaker_tracker import SpeakerTracker
from ..tracking.temporal_face_tracker import TemporalFaceTracker
from ..video.temporal_video_generator import TemporalVideoGenerator
from ..crop.calculator import CropWindowCalculator
from ..config.settings import ReframingConfig


class Reframer:
    """
    Core Re-framer for intelligent video reframing

    Enhanced with intelligent face detection and tracking for creating compelling
    vertical crops (9:16 aspect ratio) optimized for YouTube Shorts and Instagram Reels.

    Features:
    - Advanced face detection (MediaPipe, InsightFace)
    - Primary speaker identification using transcription timing
    - Dynamic crop window with temporal smoothing
    - Motion-based fallback when faces aren't detected
    """

    def __init__(self, config: Optional[ReframingConfig] = None, pipeline_output_dir: str = "output"):
        self.config = config or ReframingConfig.from_env()
        self.pipeline_output_dir = pipeline_output_dir
        self.logger = logging.getLogger(__name__)

        self.face_engine = None
        self.speaker_tracker = None
        self.crop_calculator = None
        self._initialize_components()

    def _initialize_components(self):
        """Initialize face detection and tracking components"""
        try:
            if self.config.face_detection_enabled:
                self.face_engine = FaceDetectionEngine(
                    backend=self.config.face_detection_backend,
                    confidence_threshold=self.config.face_detection_confidence,
                    enable_gpu=getattr(self.config, 'face_detection_gpu_enabled', True)
                )
                self.crop_calculator = CropWindowCalculator(
                    smoothing_factor=self.config.face_tracking_smoothing,
                    margin=self.config.face_crop_margin
                )
                self.logger.info(f"Initialized face detection with {self.config.face_detection_backend} backend")
            else:
                self.logger.info("Face detection disabled, using center crop fallback")
        except Exception as e:
            self.logger.warning(f"Failed to initialize face detection: {str(e)}, falling back to center crop")
            self.face_engine = None
            self.crop_calculator = None

    def process(self, job_id: str, clip_result: Dict[str, Any], params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Re-frame clips for vertical video optimization with intelligent face detection

        Args:
            job_id: Unique identifier for the job
            clip_result: Results from the clip renderer
            params: Additional parameters for the task

        Returns:
            Dictionary containing re-framed clip results
        """
        self.logger.info(f"Re-framing clips for job {job_id}")

        # Load transcription data for speaker identification
        if self.config.speaker_identification_enabled:
            try:
                transcription_data = self._load_transcription_data(job_id)
                if transcription_data:
                    self.speaker_tracker = SpeakerTracker(transcription_data.get('segments', []))
                    self.logger.info(f"Loaded {len(transcription_data.get('segments', []))} transcription segments")
                else:
                    self.logger.warning("No transcription data found, speaker identification disabled")
            except Exception as e:
                self.logger.warning(f"Failed to load transcription data: {str(e)}")
                self.speaker_tracker = None

        # Create job directory
        job_dir = os.path.join(self.pipeline_output_dir, job_id)
        reframed_dir = os.path.join(job_dir, "reframed")
        os.makedirs(reframed_dir, exist_ok=True)

        # Get clip manifest
        manifest_path = clip_result.get('manifest_path')
        if not manifest_path or not os.path.exists(manifest_path):
            raise ValueError(f"Invalid manifest path from clip renderer: {manifest_path}")

        # Load clip manifest
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)

        # Get clips from manifest
        clips = manifest.get('clips', [])
        if not clips:
            raise ValueError("No clips found in manifest")

        # Get parameters
        output_format = params.get('output_format', 'original')
        layout_mode = params.get('layout_mode', 'vertical')  # 'vertical', 'dual', 'both'

        # Skip re-framing if output format is original
        if output_format == 'original':
            self.logger.info("Skipping re-framing as output format is 'original'")

            # Create result with original clip data
            result = {
                'status': 'completed',
                'job_id': job_id,
                'reframed_dir': reframed_dir,
                'metadata': {
                    'clip_count': len(clips),
                    'output_format': 'original',
                    'clips': clips
                }
            }

            return result

        # Re-frame clips
        reframed_clips = []

        for clip in clips:
            # Get clip path
            clip_path = clip.get('file_path')
            if not clip_path or not os.path.exists(clip_path):
                self.logger.warning(f"Clip file not found: {clip_path}")
                continue

            # Re-frame clip
            reframed_clip = self._reframe_clip(clip, reframed_dir, output_format, layout_mode)
            if reframed_clip:
                reframed_clips.append(reframed_clip)

        # Create result with re-framed clip data
        result = {
            'status': 'completed',
            'job_id': job_id,
            'reframed_dir': reframed_dir,
            'metadata': {
                'clip_count': len(reframed_clips),
                'output_format': output_format,
                'clips': reframed_clips
            }
        }

        return result

    def _reframe_clip(self, clip: Dict[str, Any], output_dir: str, output_format: str, layout_mode: str = 'vertical') -> Dict[str, Any]:
        """
        Re-frame a clip for vertical or square video

        Args:
            clip: Clip data from manifest
            output_dir: Directory to save re-framed clip
            output_format: Output format ('vertical' or 'square')
            layout_mode: Layout mode for dual-face scenarios ('vertical', 'dual', 'both')

        Returns:
            Dictionary containing re-framed clip data
        """
        try:
            # Get clip path and ID
            clip_path = clip.get('file_path')
            clip_id = clip.get('clip_id')

            if not clip_path or not isinstance(clip_path, str):
                raise ValueError(f"Invalid clip path: {clip_path}")

            if not clip_id:
                raise ValueError(f"Invalid clip ID: {clip_id}")

            # Define output path - use MP4 for better compatibility and social media optimization
            output_path = os.path.join(output_dir, f"{clip_id}_reframed.mp4")

            # Get video information
            probe = ffmpeg.probe(clip_path)
            video_stream = next((stream for stream in probe['streams'] if stream['codec_type'] == 'video'), None)

            if not video_stream:
                raise ValueError("No video stream found in clip")

            # Get video dimensions
            width = int(video_stream['width'])
            height = int(video_stream['height'])

            # Check for dual-face scenario and use vertical layout if applicable
            self.logger.info(f"🔍 Checking vertical layout conditions for {clip_id}:")
            self.logger.info(f"   Output format: {output_format}")
            self.logger.info(f"   Layout mode: {layout_mode}")
            self.logger.info(f"   Face engine available: {self.face_engine is not None}")

            if output_format == 'vertical' and layout_mode in ['vertical', 'both'] and self.face_engine:
                self.logger.info(f"🎭 Attempting vertical layout processing for {clip_id}...")
                dual_face_result = self._try_vertical_layout_processing(
                    clip_path, output_path, width, height, clip_id
                )
                if dual_face_result:
                    # Vertical layout was successful
                    self.logger.info(f"✅ Vertical layout successful for {clip_id}")
                    reframed_clip = clip.copy()
                    reframed_clip['reframed_path'] = output_path
                    reframed_clip['output_format'] = 'vertical_layout'
                    reframed_clip['layout_mode'] = layout_mode
                    reframed_clip['dual_face_processing'] = True
                    return reframed_clip
                else:
                    self.logger.info(f"ℹ️ Vertical layout not suitable for {clip_id}, using traditional processing")
            else:
                self.logger.info(f"ℹ️ Vertical layout conditions not met for {clip_id}, using traditional processing")

            # Fallback to traditional processing
            # Calculate crop parameters based on output format
            if output_format == 'vertical':
                x_offset, y_offset, target_width, target_height = self._calculate_vertical_crop(
                    clip_path, width, height
                )
            elif output_format == 'square':
                x_offset, y_offset, target_width, target_height = self._calculate_square_crop(
                    clip_path, width, height
                )
            else:
                raise ValueError(f"Unsupported output format: {output_format}")

            # Apply crop and scale to video, copy audio
            self._apply_crop_and_encode(
                clip_path, output_path, x_offset, y_offset,
                target_width, target_height, output_format
            )

            # Create re-framed clip data
            reframed_clip = clip.copy()
            reframed_clip['reframed_path'] = output_path
            reframed_clip['output_format'] = output_format

            return reframed_clip

        except Exception as e:
            self.logger.error(f"Error re-framing clip {clip.get('clip_id')}: {str(e)}")
            # Return empty dict instead of None to maintain type consistency
            return {}

    def _calculate_vertical_crop(self, clip_path: str, width: int, height: int) -> tuple:
        """Calculate crop parameters for vertical video (9:16 aspect ratio)"""
        # Calculate target dimensions based on the original video aspect ratio
        original_aspect = width / height
        target_aspect = 9 / 16

        if original_aspect > target_aspect:
            # Original video is wider than 9:16, crop width and use full height
            target_width = int(height * target_aspect)
            target_height = height

            # Center crop by default
            x_offset = (width - target_width) // 2
            y_offset = 0
        else:
            # Original video is taller than 9:16, use full width and crop height
            target_width = width
            target_height = int(width / target_aspect)

            # Center crop by default
            x_offset = 0
            y_offset = (height - target_height) // 2

        # Use intelligent face detection to adjust crop if available
        if self.face_engine and self.crop_calculator and clip_path:
            try:
                self.logger.info(f"Attempting face detection for vertical crop on clip: {clip_path}")
                # Get optimal crop using face detection
                crop_window = self._calculate_intelligent_crop(
                    clip_path, width, height, target_width, target_height, 0.0
                )
                if crop_window:
                    x_offset = crop_window.x
                    y_offset = crop_window.y
                    self.logger.info(f"✅ Face-based vertical crop applied: x={x_offset}, y={y_offset}, confidence={crop_window.confidence:.2f}")
                else:
                    self.logger.warning("❌ No face detected, using center crop")
            except Exception as e:
                self.logger.warning(f"❌ Face detection failed, using center crop: {str(e)}")
        else:
            self.logger.info("Face detection not available, using center crop")

        # Ensure offsets are within bounds
        x_offset = max(0, min(width - target_width, x_offset))
        y_offset = max(0, min(height - target_height, y_offset))

        return x_offset, y_offset, target_width, target_height

    def _calculate_square_crop(self, clip_path: str, width: int, height: int) -> tuple:
        """Calculate crop parameters for square video (1:1 aspect ratio)"""
        # For square video (1:1 aspect ratio)
        target_size = min(width, height)

        # Center crop by default
        x_offset = (width - target_size) // 2
        y_offset = (height - target_size) // 2

        # Use intelligent face detection to adjust crop if available
        if self.face_engine and self.crop_calculator and clip_path:
            try:
                # Get optimal crop using face detection
                crop_window = self._calculate_intelligent_crop(
                    clip_path, width, height, target_size, target_size, 0.0
                )
                if crop_window:
                    x_offset = crop_window.x
                    y_offset = crop_window.y
                    self.logger.debug(f"Face-based square crop: x={x_offset}, y={y_offset}, confidence={crop_window.confidence:.2f}")
            except Exception as e:
                self.logger.warning(f"Face detection failed, using center crop: {str(e)}")

        # Ensure offsets are within bounds
        x_offset = max(0, min(width - target_size, x_offset))
        y_offset = max(0, min(height - target_size, y_offset))

        return x_offset, y_offset, target_size, target_size

    def _apply_crop_and_encode(self, input_path: str, output_path: str, x_offset: int, y_offset: int,
                             target_width: int, target_height: int, output_format: str):
        """Apply crop and encode video with optimized settings"""
        input_stream = ffmpeg.input(input_path)

        if output_format == 'vertical':
            # For vertical video, scale to standard vertical dimensions
            video = input_stream.video.crop(x_offset, y_offset, target_width, target_height).filter('scale', self.config.video_width, self.config.video_height)
        else:  # square
            # For square video, scale to 720x720
            video = input_stream.video.crop(x_offset, y_offset, target_width, target_height).filter('scale', 720, 720)

        audio = input_stream.audio

        (
            ffmpeg
            .output(
                video, audio,
                output_path,
                vcodec='libx264',  # Use H.264 codec for video
                acodec='aac',      # Use AAC codec for audio
                preset='fast',     # Fast encoding preset
                crf=23,            # Constant Rate Factor (quality)
                f='mp4',           # Use MP4 format for better compatibility and social media
                movflags='+faststart'  # Optimize for streaming
            )
            .global_args('-y')  # Overwrite output file if it exists
            .run(quiet=True, overwrite_output=True)
        )

    def _load_transcription_data(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Load transcription data from the job directory"""
        try:
            job_dir = os.path.join(self.pipeline_output_dir, job_id)
            transcript_path = os.path.join(job_dir, "transcript", "transcript.json")

            if os.path.exists(transcript_path):
                with open(transcript_path, 'r') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"Transcript file not found: {transcript_path}")
                return None
        except Exception as e:
            self.logger.error(f"Error loading transcription data: {str(e)}")
            return None

    def _calculate_intelligent_crop(self, clip_path: str, frame_width: int, frame_height: int,
                                  target_width: int, target_height: int, timestamp: float) -> Optional[Any]:
        """
        Calculate intelligent crop window using face detection and speaker tracking

        Args:
            clip_path: Path to the video clip
            frame_width: Original frame width
            frame_height: Original frame height
            target_width: Target crop width
            target_height: Target crop height
            timestamp: Current timestamp in the clip

        Returns:
            CropWindow object or None if face detection fails
        """
        try:
            # Extract multiple frames for better face detection
            cap = cv2.VideoCapture(clip_path)
            if not cap.isOpened():
                self.logger.warning(f"Could not open video file: {clip_path}")
                return None

            # Get total frame count and fps
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            if total_frames <= 0 or fps <= 0:
                cap.release()
                self.logger.warning(f"Invalid video properties: frames={total_frames}, fps={fps}")
                return None

            # Sample multiple frames for better face detection
            frame_positions = []
            if total_frames >= 5:
                # Sample 5 frames: 20%, 35%, 50%, 65%, 80% through the clip
                frame_positions = [
                    int(total_frames * 0.2),
                    int(total_frames * 0.35),
                    int(total_frames * 0.5),
                    int(total_frames * 0.65),
                    int(total_frames * 0.8)
                ]
            else:
                # For very short clips, sample all available frames
                frame_positions = list(range(total_frames))

            best_face = None
            best_confidence = 0.0
            frames_processed = 0
            best_group_bounds = None

            for frame_pos in frame_positions:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                ret, frame = cap.read()

                if not ret or frame is None:
                    continue

                frames_processed += 1
                current_timestamp = frame_pos / fps

                # Detect faces in this frame
                faces = []
                if self.face_engine:
                    faces = self.face_engine.detect_faces(frame)
                    self.logger.debug(f"Frame {frame_pos}: Detected {len(faces)} faces")

                if faces:
                    # Calculate group bounds for multi-face tracking
                    group_bounds = None
                    if self.face_engine:
                        group_bounds = self.face_engine.calculate_group_face_bounds(faces, frame_width, frame_height)

                    # Log multi-face detection
                    if len(faces) >= 2 and group_bounds:
                        self.logger.info(f"🎭 Multi-face detected: {len(faces)} faces, group bounds: {group_bounds.width}x{group_bounds.height}")

                    # Store group bounds for later use
                    if group_bounds:
                        if not best_group_bounds:
                            best_group_bounds = group_bounds
                        elif group_bounds.confidence > best_group_bounds.confidence:
                            best_group_bounds = group_bounds

                    # Find the best face in this frame
                    if self.speaker_tracker:
                        primary_face = self.speaker_tracker.identify_primary_speaker(
                            faces, current_timestamp, frame_width, frame_height
                        )
                    else:
                        # Simple fallback: choose the largest face
                        primary_face = max(faces, key=lambda f: f.width * f.height)

                    if primary_face and primary_face.confidence > best_confidence:
                        best_face = primary_face
                        best_confidence = primary_face.confidence

            cap.release()

            # Calculate crop window using the best face found
            if (best_face or best_group_bounds) and self.crop_calculator:
                crop_window = self.crop_calculator.calculate_crop_window(
                    best_face, frame_width, frame_height, target_width, target_height, timestamp, best_group_bounds
                )
                return crop_window

            return None

        except Exception as e:
            self.logger.error(f"Error in intelligent crop calculation: {str(e)}")
            return None

    def _try_vertical_layout_processing(self, clip_path: str, output_path: str,
                                      width: int, height: int, clip_id: str) -> bool:
        """
        Try to process the clip using vertical layout for dual-face scenarios

        Args:
            clip_path: Path to the video clip
            output_path: Output path for the processed video
            width: Video width
            height: Video height
            clip_id: Clip identifier

        Returns:
            True if vertical layout processing was successful
        """
        try:
            # Import here to avoid circular imports
            from ..video.vertical_layout_compositor import VerticalLayoutCompositor

            # Sample frames to detect faces and determine if this is a dual-face scenario
            self.logger.info(f"🔍 Sampling faces for vertical layout analysis...")
            face_data_sequence = self._sample_faces_for_vertical_layout(clip_path, width, height)

            if not face_data_sequence:
                self.logger.info(f"❌ No face data found for clip {clip_id}")
                return False

            # Analyze face data
            dual_face_frames = sum(1 for data in face_data_sequence if len(data.get('faces', [])) == 2)
            total_frames = len(face_data_sequence)
            dual_face_ratio = dual_face_frames / total_frames if total_frames > 0 else 0

            self.logger.info(f"📊 Face analysis for {clip_id}:")
            self.logger.info(f"   Total frames sampled: {total_frames}")
            self.logger.info(f"   Dual-face frames: {dual_face_frames}")
            self.logger.info(f"   Dual-face ratio: {dual_face_ratio:.2%}")
            self.logger.info(f"   Threshold required: 70%")

            # Check if this is suitable for vertical layout
            if not VerticalLayoutCompositor.is_vertical_layout_scenario(face_data_sequence):
                self.logger.info(f"❌ Clip {clip_id} not suitable for vertical layout (insufficient dual faces)")
                return False

            self.logger.info(f"🎭 Detected dual-face scenario for clip {clip_id}, using vertical layout")

            # Create vertical layout compositor
            compositor = VerticalLayoutCompositor()

            # Create temporary output directory
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                # Process with vertical layout
                vertical_output = compositor.process_vertical_layout_video(
                    input_video_path=clip_path,
                    face_data_sequence=face_data_sequence,
                    output_dir=temp_dir,
                    base_filename=clip_id
                )

                # Move the result to the final output path
                import shutil
                shutil.move(vertical_output.video_path, output_path)

                self.logger.info(f"✅ Successfully created vertical layout for clip {clip_id}")
                return True

        except Exception as e:
            self.logger.warning(f"❌ Vertical layout processing failed for clip {clip_id}: {str(e)}")
            return False

    def _sample_faces_for_vertical_layout(self, clip_path: str, width: int, height: int) -> List[Dict[str, Any]]:
        """
        Sample faces from the clip to determine if it's suitable for vertical layout

        Args:
            clip_path: Path to the video clip
            width: Video width
            height: Video height

        Returns:
            List of face data sequences
        """
        try:
            import cv2

            self.logger.info(f"🎬 Opening video for face sampling: {clip_path}")
            cap = cv2.VideoCapture(clip_path)
            if not cap.isOpened():
                self.logger.error(f"❌ Could not open video: {clip_path}")
                return []

            # Get video properties
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)

            self.logger.info(f"📊 Video properties: {total_frames} frames, {fps} fps")

            if total_frames <= 0 or fps <= 0:
                self.logger.error(f"❌ Invalid video properties: frames={total_frames}, fps={fps}")
                cap.release()
                return []

            # Sample frames at regular intervals (every 1 second, max 10 samples)
            sample_interval = max(1, int(fps))
            max_samples = min(10, total_frames // sample_interval)

            self.logger.info(f"🔍 Sampling {max_samples} frames at {sample_interval} frame intervals")

            face_data_sequence = []

            for i in range(max_samples):
                frame_pos = i * sample_interval
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_pos)
                ret, frame = cap.read()

                if not ret or frame is None:
                    self.logger.warning(f"⚠️ Could not read frame at position {frame_pos}")
                    continue

                timestamp = frame_pos / fps

                # Detect faces in this frame
                faces = []
                if self.face_engine:
                    detected_faces = self.face_engine.detect_faces(frame)
                    faces = [self._face_detection_to_dict(face) for face in detected_faces]
                    self.logger.debug(f"Frame {frame_pos}: Detected {len(faces)} faces")
                else:
                    self.logger.warning("⚠️ No face engine available for detection")

                face_data_sequence.append({
                    'timestamp': timestamp,
                    'frame_index': frame_pos,
                    'faces': faces
                })

            cap.release()
            return face_data_sequence

        except Exception as e:
            self.logger.error(f"Error sampling faces for vertical layout: {str(e)}")
            return []

    def _face_detection_to_dict(self, face_detection) -> Dict[str, Any]:
        """Convert FaceDetection object to dictionary"""
        return {
            'x': face_detection.x,
            'y': face_detection.y,
            'width': face_detection.width,
            'height': face_detection.height,
            'confidence': face_detection.confidence,
            'center_x': face_detection.center_x,
            'center_y': face_detection.center_y
        }

    def process_with_temporal_tracking(self, job_id: str, clip_result: Dict[str, Any],
                                     params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Re-frame clips using temporal face tracking for smooth camera movements

        Args:
            job_id: Unique identifier for the job
            clip_result: Results from the clip renderer
            params: Additional parameters including temporal tracking settings

        Returns:
            Dictionary containing re-framed clip results with temporal tracking
        """
        self.logger.info(f"Re-framing clips with temporal tracking for job {job_id}")

        # Get temporal tracking parameters
        detection_interval = params.get('temporal_detection_interval', 1.0)
        smoothing_factor = params.get('temporal_smoothing_factor', 0.3)
        use_existing_tracking = params.get('use_existing_tracking', False)

        # Override face detection settings from params for perfect highlights
        face_backend = params.get('face_detection_backend', self.config.face_detection_backend)
        face_confidence = params.get('face_detection_confidence', self.config.face_detection_confidence)

        self.logger.info(f"Using face detection backend: {face_backend}")
        self.logger.info(f"Using face detection confidence: {face_confidence}")
        self.logger.info(f"Detection interval: {detection_interval}s")
        self.logger.info(f"Smoothing factor: {smoothing_factor}")

        # Create job directory
        job_dir = os.path.join(self.pipeline_output_dir, job_id)
        reframed_dir = os.path.join(job_dir, "reframed")
        tracking_dir = os.path.join(job_dir, "temporal_tracking")
        os.makedirs(reframed_dir, exist_ok=True)
        os.makedirs(tracking_dir, exist_ok=True)

        # Get clip manifest
        manifest_path = clip_result.get('manifest_path')
        if not manifest_path or not os.path.exists(manifest_path):
            raise ValueError(f"Invalid manifest path from clip renderer: {manifest_path}")

        # Load clip manifest
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)

        # Get clips from manifest
        clips = manifest.get('clips', [])
        if not clips:
            raise ValueError("No clips found in manifest")

        # Get parameters
        output_format = params.get('output_format', 'vertical')

        # Initialize temporal face tracker with custom parameters for perfect highlights
        try:
            # Create a custom face engine with the specified parameters
            custom_face_engine = FaceDetectionEngine(
                backend=face_backend,
                confidence_threshold=face_confidence
            )
            temporal_tracker = TemporalFaceTracker(custom_face_engine, detection_interval)
            video_generator = TemporalVideoGenerator(crop_calculator=self.crop_calculator)
            self.logger.info("Successfully initialized temporal face tracker with custom parameters")
        except Exception as e:
            self.logger.error(f"Failed to initialize temporal face tracker: {str(e)}")
            if self.face_engine:
                # Fallback to default face engine
                temporal_tracker = TemporalFaceTracker(self.face_engine, detection_interval)
                video_generator = TemporalVideoGenerator(crop_calculator=self.crop_calculator)
                self.logger.info("Using fallback face engine for temporal tracking")
            else:
                self.logger.error("No face detection engine available for temporal tracking")
                return self.process(job_id, clip_result, params)  # Fallback to regular processing

        # Process clips with temporal tracking
        reframed_clips = []

        for clip in clips:
            clip_path = clip.get('file_path')
            if not clip_path or not os.path.exists(clip_path):
                self.logger.warning(f"Clip file not found: {clip_path}")
                continue

            try:
                # Generate or load tracking data
                clip_id = clip.get('clip_id', 'unknown')
                tracking_json_path = os.path.join(tracking_dir, f"{clip_id}_tracking.json")

                if use_existing_tracking and os.path.exists(tracking_json_path):
                    self.logger.info(f"Loading existing tracking data for clip {clip_id}")
                    tracking_sequence = temporal_tracker.load_tracking_data(tracking_json_path)
                else:
                    self.logger.info(f"Generating temporal tracking data for clip {clip_id}")
                    tracking_sequence = temporal_tracker.track_faces_in_video(clip_path)
                    temporal_tracker.save_tracking_data(tracking_sequence, tracking_json_path)

                # Generate video with temporal tracking
                output_path = os.path.join(reframed_dir, f"{clip_id}_temporal_reframed.mp4")

                # Calculate target dimensions based on output format
                if output_format == 'vertical':
                    target_width = self.config.video_width  # Use configured dimensions
                    target_height = self.config.video_height
                elif output_format == 'square':
                    target_width = 720  # 1:1 aspect ratio
                    target_height = 720
                else:
                    self.logger.warning(f"Unsupported output format: {output_format}, using vertical")
                    target_width = self.config.video_width
                    target_height = self.config.video_height

                success = video_generator.generate_video_from_tracking(
                    clip_path, tracking_sequence, output_path,
                    target_width, target_height, smoothing_factor
                )

                if success:
                    # Create reframed clip data
                    reframed_clip = clip.copy()
                    reframed_clip['reframed_path'] = output_path
                    reframed_clip['output_format'] = output_format
                    reframed_clip['temporal_tracking'] = True
                    reframed_clip['tracking_data_path'] = tracking_json_path
                    reframed_clips.append(reframed_clip)

                    self.logger.info(f"✅ Successfully generated temporal tracked video: {output_path}")
                else:
                    self.logger.error(f"❌ Failed to generate temporal tracked video for clip {clip_id}")

            except Exception as e:
                self.logger.error(f"Error processing clip {clip.get('clip_id')} with temporal tracking: {str(e)}")
                continue

        # Create result
        result = {
            'status': 'completed',
            'job_id': job_id,
            'reframed_dir': reframed_dir,
            'tracking_dir': tracking_dir,
            'metadata': {
                'clip_count': len(reframed_clips),
                'output_format': output_format,
                'temporal_tracking': True,
                'detection_interval': detection_interval,
                'smoothing_factor': smoothing_factor,
                'clips': reframed_clips
            }
        }

        return result