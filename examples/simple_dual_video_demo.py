#!/usr/bin/env python3
"""
Simple Dual Video Compositing Demo

Demonstrates the core dual video compositing functionality for podcast scenarios
with exactly two faces using the enhanced face positioning system.
"""

import sys
import os
import logging
import json
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from reframing.video.dual_video_compositor import DualVideoCompositor
from reframing.video.enhanced_dual_positioning import EnhancedDualFacePositioning

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_sample_podcast_face_data():
    """Create sample face data for a podcast scenario with two speakers"""
    logger.info("📊 Creating sample podcast face data with two speakers")
    
    face_data = []
    duration = 10.0  # 10 seconds
    fps = 30
    total_frames = int(duration * fps)
    
    for frame in range(total_frames):
        timestamp = frame / fps
        
        # Host (left speaker) - relatively stationary
        host_face = {
            'x': 350 + int(5 * (frame % 30) / 30),  # Slight movement
            'y': 400,
            'width': 220,
            'height': 220,
            'confidence': 0.94,
            'center_x': 460 + int(5 * (frame % 30) / 30),
            'center_y': 510
        }
        
        # Guest (right speaker) - slight gesturing
        guest_face = {
            'x': 1200 + int(10 * (frame % 20) / 20),  # More movement
            'y': 380 + int(8 * (frame % 25) / 25),
            'width': 200,
            'height': 200,
            'confidence': 0.91,
            'center_x': 1300 + int(10 * (frame % 20) / 20),
            'center_y': 480 + int(8 * (frame % 25) / 25)
        }
        
        face_data.append({
            'timestamp': timestamp,
            'frame_index': frame,
            'faces': [host_face, guest_face]
        })
    
    logger.info(f"✅ Created {len(face_data)} frames of dual-face data")
    return face_data


def demonstrate_dual_face_detection():
    """Demonstrate dual face scenario detection"""
    logger.info("🔍 Demonstrating Dual Face Scenario Detection")
    logger.info("=" * 50)
    
    # Create sample data
    face_data = create_sample_podcast_face_data()
    
    # Test dual face detection
    is_dual_scenario = DualVideoCompositor.is_dual_face_scenario(face_data)
    
    # Calculate statistics
    total_frames = len(face_data)
    dual_face_frames = sum(1 for frame in face_data if len(frame.get('faces', [])) == 2)
    dual_face_ratio = dual_face_frames / total_frames
    
    logger.info(f"📊 Detection Results:")
    logger.info(f"   • Total frames: {total_frames}")
    logger.info(f"   • Dual-face frames: {dual_face_frames}")
    logger.info(f"   • Dual-face ratio: {dual_face_ratio:.1%}")
    logger.info(f"   • Dual scenario detected: {is_dual_scenario}")
    
    return face_data, is_dual_scenario


def demonstrate_face_track_separation():
    """Demonstrate face track separation for dual video processing"""
    logger.info("\n👥 Demonstrating Face Track Separation")
    logger.info("=" * 45)
    
    # Create compositor
    compositor = DualVideoCompositor()
    
    # Create sample data
    face_data = create_sample_podcast_face_data()
    
    # Separate face tracks
    left_track, right_track = compositor._separate_face_tracks(face_data)
    
    logger.info(f"📈 Face Track Analysis:")
    logger.info(f"   Left Track (Host):")
    logger.info(f"   • Face ID: {left_track.face_id}")
    logger.info(f"   • Target region: {left_track.target_region}")
    logger.info(f"   • Detections: {len(left_track.face_detections)}")
    logger.info(f"   • Average X position: {sum(f.center_x for f in left_track.face_detections) / len(left_track.face_detections):.0f}")
    
    logger.info(f"   Right Track (Guest):")
    logger.info(f"   • Face ID: {right_track.face_id}")
    logger.info(f"   • Target region: {right_track.target_region}")
    logger.info(f"   • Detections: {len(right_track.face_detections)}")
    logger.info(f"   • Average X position: {sum(f.center_x for f in right_track.face_detections) / len(right_track.face_detections):.0f}")
    
    # Generate individual layouts
    logger.info(f"\n🎯 Generating individual face layouts...")
    compositor._generate_individual_layouts(left_track, right_track)
    
    logger.info(f"   Generated layouts:")
    logger.info(f"   • Left track: {len(left_track.layouts)} layouts")
    logger.info(f"   • Right track: {len(right_track.layouts)} layouts")
    
    # Calculate confidence scores
    left_confidence = compositor._calculate_average_confidence(left_track)
    right_confidence = compositor._calculate_average_confidence(right_track)
    
    logger.info(f"   Quality metrics:")
    logger.info(f"   • Left track confidence: {left_confidence:.3f}")
    logger.info(f"   • Right track confidence: {right_confidence:.3f}")
    
    return left_track, right_track


def demonstrate_metadata_generation():
    """Demonstrate metadata and temporal alignment generation"""
    logger.info("\n📊 Demonstrating Metadata Generation")
    logger.info("=" * 45)
    
    # Create compositor and data
    compositor = DualVideoCompositor()
    face_data = create_sample_podcast_face_data()
    
    # Separate tracks and generate layouts
    left_track, right_track = compositor._separate_face_tracks(face_data)
    compositor._generate_individual_layouts(left_track, right_track)
    
    # Create metadata
    metadata = compositor._create_dual_video_metadata(left_track, right_track, face_data)
    
    logger.info(f"📋 Generated Metadata:")
    gen_info = metadata['generation_info']
    logger.info(f"   Generation Info:")
    logger.info(f"   • Compositor version: {gen_info['compositor_version']}")
    logger.info(f"   • Face positioning engine: {gen_info['face_positioning_engine']}")
    logger.info(f"   • Total frames: {gen_info['total_frames']}")
    
    video_a = metadata['video_a']
    video_b = metadata['video_b']
    logger.info(f"   Video A (Host - Left Face):")
    logger.info(f"   • Target region: {video_a['target_region']}")
    logger.info(f"   • Region dimensions: {video_a['region_dimensions']}")
    logger.info(f"   • Average confidence: {video_a['average_confidence']:.3f}")
    
    logger.info(f"   Video B (Guest - Right Face):")
    logger.info(f"   • Target region: {video_b['target_region']}")
    logger.info(f"   • Region dimensions: {video_b['region_dimensions']}")
    logger.info(f"   • Average confidence: {video_b['average_confidence']:.3f}")
    
    # Create temporal alignment
    alignment = compositor._create_temporal_alignment_data(left_track, right_track)
    
    frame_mapping = alignment['frame_mapping']
    logger.info(f"   Temporal Alignment:")
    logger.info(f"   • Synchronized frames: {frame_mapping['synchronized_frames']}")
    logger.info(f"   • Compositing ready: {alignment['compositing_ready']}")
    
    return metadata, alignment


def demonstrate_enhanced_dual_positioning():
    """Demonstrate enhanced dual face positioning integration"""
    logger.info("\n🎯 Demonstrating Enhanced Dual Positioning")
    logger.info("=" * 50)
    
    # Create enhanced positioning system
    enhanced_positioning = EnhancedDualFacePositioning()
    
    # Create sample data
    face_data = create_sample_podcast_face_data()
    
    # Get processing recommendations
    recommendations = enhanced_positioning.get_processing_recommendations(face_data)
    
    logger.info(f"📋 Processing Recommendations:")
    analysis = recommendations['analysis']
    logger.info(f"   • Dual-face scenario: {analysis['is_dual_face_scenario']}")
    logger.info(f"   • Dual-face ratio: {analysis['dual_face_ratio']:.1%}")
    logger.info(f"   • Recommendation: {analysis['recommendation']}")
    
    logger.info(f"   Processing Options:")
    for option, enabled in recommendations['processing_options'].items():
        status = "✅" if enabled else "❌"
        logger.info(f"   {status} {option.replace('_', ' ').title()}")
    
    logger.info(f"   Expected Benefits:")
    for benefit in recommendations['expected_benefits']:
        logger.info(f"   • {benefit}")
    
    # Test configuration
    logger.info(f"\n🔧 Configuration Options:")
    debug_info = enhanced_positioning.get_debug_info()
    config = debug_info['enhanced_dual_positioning']
    logger.info(f"   • Dual face threshold: {config['dual_face_threshold']}")
    logger.info(f"   • Dual output enabled: {config['enable_dual_output']}")
    logger.info(f"   • Preserve single output: {config['preserve_single_output']}")
    
    return recommendations


def demonstrate_crop_filter_generation():
    """Demonstrate crop filter generation for video processing"""
    logger.info("\n🎬 Demonstrating Crop Filter Generation")
    logger.info("=" * 50)
    
    # Create compositor and data
    compositor = DualVideoCompositor()
    face_data = create_sample_podcast_face_data()
    
    # Separate tracks and generate layouts
    left_track, right_track = compositor._separate_face_tracks(face_data)
    compositor._generate_individual_layouts(left_track, right_track)
    
    # Generate crop filters
    left_filter = compositor._create_face_track_crop_filter(left_track)
    right_filter = compositor._create_face_track_crop_filter(right_track)
    
    logger.info(f"🎥 Generated Crop Filters:")
    logger.info(f"   Left face filter: {left_filter}")
    logger.info(f"   Right face filter: {right_filter}")
    
    # Show what the actual video generation would look like
    logger.info(f"\n📹 Video Generation Preview:")
    logger.info(f"   Video A (Left Face):")
    logger.info(f"   • Target: podcast_left_face.mp4")
    logger.info(f"   • Resolution: 720x1280")
    logger.info(f"   • Region: Top half (720x640)")
    logger.info(f"   • Crop filter: {left_filter}")
    
    logger.info(f"   Video B (Right Face):")
    logger.info(f"   • Target: podcast_right_face.mp4")
    logger.info(f"   • Resolution: 720x1280")
    logger.info(f"   • Region: Bottom half (720x640)")
    logger.info(f"   • Crop filter: {right_filter}")
    
    return left_filter, right_filter


def main():
    """Main demonstration function"""
    logger.info("🎙️ Simple Dual Video Compositing Demo")
    logger.info("=" * 50)
    logger.info("Podcast scenario with enhanced face positioning")
    
    try:
        # Run all demonstrations
        face_data, is_dual = demonstrate_dual_face_detection()
        left_track, right_track = demonstrate_face_track_separation()
        metadata, alignment = demonstrate_metadata_generation()
        recommendations = demonstrate_enhanced_dual_positioning()
        left_filter, right_filter = demonstrate_crop_filter_generation()
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("🎉 DUAL VIDEO COMPOSITING DEMO COMPLETE")
        logger.info("="*60)
        logger.info("✅ All core functionality demonstrated successfully!")
        
        logger.info("\n🎯 Key Features Validated:")
        logger.info("   ✅ Automatic dual-face scenario detection")
        logger.info("   ✅ Face identification by horizontal position")
        logger.info("   ✅ Face track separation (left/right)")
        logger.info("   ✅ Individual layout generation with predictive tracking")
        logger.info("   ✅ Comprehensive metadata creation")
        logger.info("   ✅ Temporal alignment for synchronization")
        logger.info("   ✅ Enhanced dual positioning integration")
        logger.info("   ✅ Crop filter generation for video processing")
        
        logger.info("\n📊 Processing Results:")
        logger.info(f"   • Dual-face scenario detected: {is_dual}")
        logger.info(f"   • Face tracks created: 2 (left + right)")
        logger.info(f"   • Layouts generated: {len(left_track.layouts)} per track")
        logger.info(f"   • Metadata fields: {len(metadata)} main sections")
        logger.info(f"   • Temporal alignment: {alignment['compositing_ready']}")
        
        logger.info("\n🎬 Ready for Video Generation:")
        logger.info("   • Input: podcast video with two faces")
        logger.info("   • Output A: podcast_left_face.mp4 (720x1280)")
        logger.info("   • Output B: podcast_right_face.mp4 (720x1280)")
        logger.info("   • Composite: podcast_composite.mp4 (vertically stacked)")
        logger.info("   • Metadata: podcast_dual_metadata.json")
        
    except Exception as e:
        logger.error(f"❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
