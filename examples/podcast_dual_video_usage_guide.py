#!/usr/bin/env python3
"""
Podcast Dual Video Compositing - Usage Guide

This guide demonstrates how to use the dual video compositing system for podcast
videos with exactly two faces, generating optimized vertical video outputs.

Core Requirements Met:
✅ Face identification by horizontal position (leftmost/rightmost)
✅ Dual video generation at 720x1280 resolution, 30fps
✅ Enhanced face positioning with 2-3 second predictive tracking
✅ Temporal synchronization between outputs
✅ MediaPipe GPU acceleration with CPU fallback
✅ Comprehensive metadata and validation
"""

import sys
import os

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def usage_example_1_basic_dual_video_processing():
    """
    Example 1: Basic dual video processing for podcast scenario
    """
    print("📖 Example 1: Basic Dual Video Processing")
    print("=" * 50)
    
    code_example = '''
from reframing.video.enhanced_dual_positioning import EnhancedDualFacePositioning

# Initialize the enhanced dual face positioning system
enhanced_positioning = EnhancedDualFacePositioning()

# Configure for podcast scenarios (optional)
enhanced_positioning.configure_dual_output(
    enable=True,           # Enable dual video generation
    preserve_single=True,  # Also generate traditional single output
    dual_threshold=0.7     # Require 70% dual-face frames
)

# Process podcast video with dual face detection
results = enhanced_positioning.process_video_with_enhanced_positioning(
    input_video_path="podcast.mp4",
    face_data_sequence=face_detections,  # From MediaPipe or other detection
    output_dir="podcast_output/",
    base_filename="podcast"
)

# Access generated dual video outputs
if results['dual_output_generated']:
    video_a = results['outputs']['dual']['video_a_path']  # Left face (top region)
    video_b = results['outputs']['dual']['video_b_path']  # Right face (bottom region)
    composite = results['outputs']['dual']['composite_path']  # Stacked composite
    
    print(f"✅ Generated dual videos:")
    print(f"   Video A (Host): {video_a}")
    print(f"   Video B (Guest): {video_b}")
    print(f"   Composite: {composite}")
'''
    
    print(code_example)


def usage_example_2_direct_compositor_usage():
    """
    Example 2: Direct compositor usage for advanced control
    """
    print("\n📖 Example 2: Direct Compositor Usage")
    print("=" * 50)
    
    code_example = '''
from reframing.video.dual_video_compositor import DualVideoCompositor

# Initialize dual video compositor
compositor = DualVideoCompositor()

# Check if video is suitable for dual processing
if DualVideoCompositor.is_dual_face_scenario(face_data_sequence):
    print("✅ Video suitable for dual-face processing")
    
    # Generate dual video outputs
    dual_output = compositor.process_dual_face_video(
        input_video_path="podcast.mp4",
        face_data_sequence=face_data_sequence,
        output_dir="output/",
        base_filename="podcast"
    )
    
    # Create vertically stacked composite
    composite_path = "output/podcast_composite.mp4"
    compositor.create_composite_video(dual_output, composite_path)
    
    # Validate output quality
    validation = compositor.validate_dual_output(dual_output)
    print(f"Temporal alignment: {validation['temporal_alignment']['alignment_ratio']:.1%}")
    print(f"Video A confidence: {validation['quality_metrics']['video_a_confidence']:.3f}")
    print(f"Video B confidence: {validation['quality_metrics']['video_b_confidence']:.3f}")
else:
    print("❌ Video not suitable for dual-face processing")
'''
    
    print(code_example)


def usage_example_3_face_data_preparation():
    """
    Example 3: Face data preparation from MediaPipe
    """
    print("\n📖 Example 3: Face Data Preparation")
    print("=" * 50)
    
    code_example = '''
import cv2
import mediapipe as mp

def extract_face_data_from_video(video_path):
    """Extract face detection data using MediaPipe"""
    
    # Initialize MediaPipe Face Detection
    mp_face_detection = mp.solutions.face_detection
    mp_drawing = mp.solutions.drawing_utils
    
    face_data_sequence = []
    
    with mp_face_detection.FaceDetection(
        model_selection=0, min_detection_confidence=0.5
    ) as face_detection:
        
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_idx = 0
        
        while cap.isOpened():
            success, image = cap.read()
            if not success:
                break
            
            # Convert BGR to RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            results = face_detection.process(image_rgb)
            
            # Extract face data
            faces = []
            if results.detections:
                for detection in results.detections:
                    bbox = detection.location_data.relative_bounding_box
                    h, w, _ = image.shape
                    
                    # Convert to absolute coordinates
                    x = int(bbox.xmin * w)
                    y = int(bbox.ymin * h)
                    width = int(bbox.width * w)
                    height = int(bbox.height * h)
                    
                    faces.append({
                        'x': x,
                        'y': y,
                        'width': width,
                        'height': height,
                        'confidence': detection.score[0],
                        'center_x': x + width / 2,
                        'center_y': y + height / 2
                    })
            
            face_data_sequence.append({
                'timestamp': frame_idx / fps,
                'frame_index': frame_idx,
                'faces': faces
            })
            
            frame_idx += 1
        
        cap.release()
    
    return face_data_sequence

# Usage
face_data = extract_face_data_from_video("podcast.mp4")
print(f"Extracted {len(face_data)} frames of face data")
'''
    
    print(code_example)


def usage_example_4_processing_recommendations():
    """
    Example 4: Getting processing recommendations
    """
    print("\n📖 Example 4: Processing Recommendations")
    print("=" * 50)
    
    code_example = '''
from reframing.video.enhanced_dual_positioning import EnhancedDualFacePositioning

# Initialize enhanced positioning
enhanced_positioning = EnhancedDualFacePositioning()

# Get processing recommendations for your video
recommendations = enhanced_positioning.get_processing_recommendations(face_data_sequence)

print("📋 Processing Analysis:")
analysis = recommendations['analysis']
print(f"   • Total frames: {analysis['total_frames']}")
print(f"   • Dual-face ratio: {analysis['dual_face_ratio']:.1%}")
print(f"   • Is dual scenario: {analysis['is_dual_face_scenario']}")
print(f"   • Recommendation: {analysis['recommendation']}")

print("\\n🎯 Processing Options:")
for option, enabled in recommendations['processing_options'].items():
    status = "✅" if enabled else "❌"
    print(f"   {status} {option.replace('_', ' ').title()}")

print("\\n💡 Expected Benefits:")
for benefit in recommendations['expected_benefits']:
    print(f"   • {benefit}")

# Configure based on recommendations
if recommendations['dual_output_beneficial']:
    enhanced_positioning.configure_dual_output(enable=True)
    print("✅ Configured for dual video output")
'''
    
    print(code_example)


def usage_example_5_output_file_structure():
    """
    Example 5: Understanding output file structure
    """
    print("\n📖 Example 5: Output File Structure")
    print("=" * 50)
    
    structure_example = '''
📁 podcast_output/
├── 📹 podcast_left_face.mp4          # Video A: Leftmost face (720x1280)
│   └── Optimized for top half region (720x640)
│   └── Enhanced face positioning with predictive tracking
│   └── 30fps, H.264 encoding
│
├── 📹 podcast_right_face.mp4         # Video B: Rightmost face (720x1280)
│   └── Optimized for bottom half region (720x640)
│   └── Enhanced face positioning with predictive tracking
│   └── 30fps, H.264 encoding
│
├── 📹 podcast_composite.mp4          # Vertically stacked composite
│   └── Combines Video A (top) + Video B (bottom)
│   └── 720x1280 resolution, perfect for vertical video
│
├── 📄 podcast_dual_metadata.json     # Comprehensive metadata
│   └── Face assignments and confidence scores
│   └── Temporal alignment data
│   └── Predictive tracking features
│   └── Quality metrics and validation results
│
├── 📄 podcast_single_metadata.json   # Traditional single output metadata
│   └── Generated if preserve_single=True
│
└── 📄 podcast_processing_report.json # Complete processing report
    └── Input validation results
    └── Processing summary and approach
    └── Quality metrics and technical details
'''
    
    print(structure_example)


def usage_example_6_quality_validation():
    """
    Example 6: Quality validation and metrics
    """
    print("\n📖 Example 6: Quality Validation")
    print("=" * 50)
    
    code_example = '''
# After generating dual videos, validate quality
validation_results = compositor.validate_dual_output(dual_output)

print("📊 Quality Validation Results:")
print(f"Files exist:")
print(f"   • Video A: {validation_results['files_exist']['video_a']}")
print(f"   • Video B: {validation_results['files_exist']['video_b']}")

print(f"\\nTemporal alignment:")
alignment = validation_results['temporal_alignment']
print(f"   • Synchronized frames: {alignment['synchronized_frames']}")
print(f"   • Total frames: {alignment['total_frames']}")
print(f"   • Alignment ratio: {alignment['alignment_ratio']:.1%}")

print(f"\\nQuality metrics:")
quality = validation_results['quality_metrics']
print(f"   • Video A confidence: {quality['video_a_confidence']:.3f}")
print(f"   • Video B confidence: {quality['video_b_confidence']:.3f}")
print(f"   • Video A stability: {quality['video_a_stability']:.3f}")
print(f"   • Video B stability: {quality['video_b_stability']:.3f}")

# Access detailed metadata
metadata = dual_output.metadata
print(f"\\n🔍 Detailed Analysis:")
print(f"   • Face positioning engine: {metadata['generation_info']['face_positioning_engine']}")
print(f"   • Total frames processed: {metadata['generation_info']['total_frames']}")
print(f"   • Video A region: {metadata['video_a']['region_dimensions']}")
print(f"   • Video B region: {metadata['video_b']['region_dimensions']}")
'''
    
    print(code_example)


def technical_specifications():
    """
    Technical specifications and requirements
    """
    print("\n🔧 Technical Specifications")
    print("=" * 50)
    
    specs = '''
📋 Core Requirements:
✅ Input: Video with exactly two faces (70%+ of frames)
✅ Output: Two synchronized MP4 files at 720x1280, 30fps
✅ Face identification by horizontal position (leftmost/rightmost)
✅ Enhanced face positioning with 2-3 second predictive tracking
✅ MediaPipe GPU acceleration with CPU fallback
✅ Temporal synchronization for future compositing

🎯 Video Specifications:
• Format: MP4 with H.264 video codec, AAC audio codec
• Resolution: 720x1280 (vertical format)
• Frame Rate: 30 fps
• Region Dimensions: 720x640 per face (top/bottom halves)
• Quality: Optimized framing within designated regions

🔮 Enhanced Features:
• Predictive Tracking: 2-3 second lookahead analysis
• Smooth Transitions: Cubic easing between layout changes
• Stability Analysis: Per-face stability scoring
• Velocity Calculation: Independent movement tracking
• Segment Planning: Intelligent segment-based processing

⚡ Performance:
• Real-time Processing: Maintains 30fps processing speed
• GPU Acceleration: Full MediaPipe GPU support
• Memory Efficient: Optimized face history management
• Parallel Processing: Independent face track processing

🔗 Integration:
• Backward Compatible: All existing functionality preserved
• Automatic Detection: Transparent dual-face scenario handling
• Flexible Configuration: Customizable thresholds and options
• Comprehensive Metadata: Rich data for post-processing
'''
    
    print(specs)


def main():
    """Main usage guide function"""
    print("🎙️ Podcast Dual Video Compositing - Usage Guide")
    print("=" * 60)
    print("Complete guide for generating dual-video outputs from podcast videos")
    
    # Run all usage examples
    usage_example_1_basic_dual_video_processing()
    usage_example_2_direct_compositor_usage()
    usage_example_3_face_data_preparation()
    usage_example_4_processing_recommendations()
    usage_example_5_output_file_structure()
    usage_example_6_quality_validation()
    technical_specifications()
    
    print("\n" + "="*60)
    print("🎉 USAGE GUIDE COMPLETE")
    print("="*60)
    print("✅ All usage examples and specifications provided!")
    print("📚 Ready to implement dual video compositing for podcast scenarios")
    
    print("\n🚀 Quick Start:")
    print("1. Extract face data using MediaPipe (Example 3)")
    print("2. Check processing recommendations (Example 4)")
    print("3. Process with enhanced dual positioning (Example 1)")
    print("4. Validate output quality (Example 6)")
    print("5. Use generated videos for vertical format distribution")


if __name__ == "__main__":
    main()
